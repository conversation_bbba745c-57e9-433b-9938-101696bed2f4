// index.js
const app = getApp()
const defaultAvatarUrl = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'
const { signInManager, recentUsedManager, pointsManager } = require('../../utils/userState');
const { getTodaySolarTerm } = require('../../utils/solarTerms');
const { getTodayInfo } = require('../../utils/lunar');
const globalState = require('../../utils/global-state');
const navigationManager = require('../../utils/navigation');
const errorHandler = require('../../utils/error-handler');

Page({
  data: {
    motto: 'Hello World',
    userInfo: {
      avatarUrl: defaultAvatarUrl,
      nickName: '',
    },
    hasUserInfo: false,
    canIUseGetUserProfile: wx.canIUse('getUserProfile'),
    canIUseNicknameComp: wx.canIUse('input.type.nickname'),
    banners: [
      {
        id: 1,
        imageUrl: 'https://img.freepik.com/free-photo/chinese-horoscope-arrangement_23-2148740061.jpg',
        title: '八字命理分析',
        url: '/pages/bazi/bazi'
      },
      {
        id: 2,
        imageUrl: 'https://img.freepik.com/free-photo/feng-shui-compass-arrangement_23-2148740065.jpg',
        title: '风水布局指南',
        url: '/pages/fengshui/fengshui'
      },
      {
        id: 3,
        imageUrl: 'https://img.freepik.com/free-photo/traditional-chinese-wedding-items_23-**********.jpg',
        title: '八字合婚测算',
        url: '/pages/marriage/marriage'
      },
      {
        id: 4,
        imageUrl: 'https://img.freepik.com/free-photo/chinese-new-year-concept-arrangement_23-**********.jpg',
        title: '易经智慧解读',
        url: '/pages/yijing/yijing'
      }
    ],
    currentDate: '',
    greeting: '',
    lunarDate: '',
    dailyFortune: {
      career: 0,
      wealth: 0,
      love: 0,
      health: 0,
      summary: ''
    },
    recentUsed: [],
    solarTerms: {},
    signInStatus: false,
    hotArticles: [],
    isLoading: true,
    categories: [
      {
        id: 1,
        name: '八字分析',
        icon: '/assets/icons/calculation/bazi.png',
        url: '/pages/bazi/bazi'
      },
      {
        id: 2,
        name: '合婚测算',
        icon: '/assets/icons/calculation/marriage.png',
        url: '/pages/marriage/marriage'
      },
      {
        id: 3,
        name: '风水布局',
        icon: '/assets/icons/calculation/fengshui.png',
        url: '/pages/fengshui/fengshui'
      },
      {
        id: 4,
        name: '易经占卜',
        icon: '/assets/icons/divination/yijing.png',
        url: '/pages/yijing/yijing'
      },
      {
        id: 5,
        name: '姓名测算',
        icon: '/assets/icons/calculation/name.png',
        url: '/pages/name-test/name-test'
      },
      {
        id: 6,
        name: '紫薇斗数',
        icon: '/assets/icons/divination/ziwei.png',
        url: '/pages/ziwei/ziwei'
      },
      {
        id: 7,
        name: '五行分析',
        icon: '/assets/icons/calculation/wuxing.png',
        url: '/pages/wuxing/wuxing'
      },
      {
        id: 8,
        name: '运势预测',
        icon: '/assets/icons/calculation/fortune.png',
        url: '/pages/fortune/fortune'
      }
    ],
    hotItems: [
      {
        id: 1,
        title: '事业运势详批',
        description: '专业分析事业发展方向',
        imageUrl: 'https://img.freepik.com/free-photo/business-concept-arrangement_23-2148740073.jpg',
        price: 98,
        orderCount: 2345,
        url: '/pages/fortune/fortune'
      },
      {
        id: 2,
        title: '感情姻缘分析',
        description: '深入解读感情运势',
        imageUrl: 'https://img.freepik.com/free-photo/love-concept-arrangement_23-2148740072.jpg',
        price: 88,
        orderCount: 3421,
        url: '/pages/marriage/marriage'
      },
      {
        id: 3,
        title: '财运提升指南',
        description: '全面分析财运走向',
        imageUrl: 'https://img.freepik.com/free-photo/wealth-concept-arrangement_23-2148740071.jpg',
        price: 108,
        orderCount: 1876,
        url: '/pages/bazi/bazi'
      }
    ],
    articles: [
      {
        id: 1,
        title: '如何利用八字改善运势',
        coverUrl: 'https://img.freepik.com/free-photo/chinese-new-year-concept_23-2148740074.jpg',
        author: '命理大师',
        viewCount: 12543
      },
      {
        id: 2,
        title: '居家风水布局的十大禁忌',
        coverUrl: 'https://img.freepik.com/free-photo/feng-shui-concept_23-2148740075.jpg',
        author: '风水专家',
        viewCount: 8976
      },
      {
        id: 3,
        title: '易经卦象在生活中的运用',
        coverUrl: 'https://img.freepik.com/free-photo/chinese-culture-concept_23-2148740076.jpg',
        author: '易经研究员',
        viewCount: 6789
      }
    ],
    // 轮播图数据
    swiperList: [
      {
        id: 1,
        image: '/assets/images/swiper/bazi.jpg',
        title: '八字命理分析',
        url: '/pages/bazi/bazi'
      },
      {
        id: 2,
        image: '/assets/images/swiper/fengshui.jpg',
        title: '风水布局指南',
        url: '/pages/fengshui/fengshui'
      },
      {
        id: 3,
        image: '/assets/images/swiper/marriage.jpg',
        title: '八字合婚测算',
        url: '/pages/marriage/marriage'
      },
      {
        id: 4,
        image: '/assets/images/swiper/yijing.jpg',
        title: '易经智慧解读',
        url: '/pages/yijing/yijing'
      }
    ],
    // 功能导航
    navList: [
      {
        id: 1,
        icon: '/assets/icons/bazi.png',
        text: '八字分析',
        url: '/pages/bazi/bazi'
      },
      {
        id: 2,
        icon: '/assets/icons/fengshui.png',
        text: '风水指南',
        url: '/pages/fengshui/fengshui'
      },
      {
        id: 3,
        icon: '/assets/icons/hehun.png',
        text: '合婚测算',
        url: '/pages/marriage/marriage'
      },
      {
        id: 4,
        icon: '/assets/icons/yijing.png',
        text: '易经解读',
        url: '/pages/yijing/yijing'
      }
    ]
  },

  onLoad() {
    this.setCurrentDate()
    this.updateGreeting()
    this.getBanners()
    this.getDailyFortune()
    this.loadRecentUsed()
    this.checkSolarTerms()
    this.checkSignIn()
    this.getHotArticles()
    
    // 设置加载状态
    setTimeout(() => {
      this.setData({ isLoading: false })
    }, 1000)

    // 检查用户是否已登录
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo,
        hasUserInfo: true
      })
    }
  },

  onShow() {
    try {
      if (typeof this.getTabBar === 'function' && this.getTabBar()) {
        this.getTabBar().setData({
          selected: 0
        })
      }
      this.loadRecentUsed() // 刷新最近使用
    } catch (error) {
      console.error('设置 TabBar 选中状态失败:', error)
    }
  },

  setCurrentDate() {
    try {
      const todayInfo = getTodayInfo();
      const { solar, lunar } = todayInfo;
      
      // 格式化公历日期
      const currentDate = `${solar.year}年${solar.month}月${solar.day}日 ${solar.weekday}`;
      
      // 格式化农历日期
      const lunarDate = `${lunar.monthCn}${lunar.dayCn} ${lunar.gzYear}${lunar.animal}年`;
      
      // 添加节气信息
      let lunarText = lunarDate;
      if (lunar.term) {
        lunarText += ` · ${lunar.term}`;
      }
      
      // 添加宜忌信息
      if (lunar.yiji) {
        const yi = lunar.yiji.yi.slice(0, 2).join('、');
        const ji = lunar.yiji.ji.slice(0, 2).join('、');
        if (yi) lunarText += ` 宜:${yi}`;
        if (ji) lunarText += ` 忌:${ji}`;
      }
      
      this.setData({
        currentDate,
        lunarDate: lunarText,
        todayInfo
      });
    } catch (error) {
      console.error('设置日期失败:', error);
      // 降级处理
      const date = new Date();
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      this.setData({
        currentDate: `${year}年${month}月${day}日`
      });
    }
  },

  // 更新问候语
  updateGreeting() {
    const hour = new Date().getHours()
    let greeting = ''
    
    if (hour >= 0 && hour < 6) {
      greeting = '凌晨好，'
    } else if (hour >= 6 && hour < 12) {
      greeting = '上午好，'
    } else if (hour >= 12 && hour < 18) {
      greeting = '下午好，'
    } else {
      greeting = '晚上好，'
    }
    
    this.setData({ greeting })
    
    // 定时更新问候语
    setTimeout(() => {
      this.updateGreeting()
    }, 3600000) // 每小时更新一次
  },

  getBanners() {
    try {
      // 使用新的主题相关轮播图
      const mockBanners = [
        { 
          id: 1, 
          imageUrl: 'https://img.freepik.com/premium-photo/chinese-feng-shui-compass-luopan-with-chinese-zodiac-signs_124507-77592.jpg',
          title: '专业八字命理分析'
        },
        { 
          id: 2, 
          imageUrl: 'https://img.freepik.com/premium-photo/modern-chinese-style-living-room-interior-design_41470-740.jpg',
          title: '风水布局指南'
        },
        { 
          id: 3, 
          imageUrl: 'https://img.freepik.com/premium-photo/traditional-chinese-wedding-ceremony_23-2148684692.jpg',
          title: '八字合婚测算'
        },
        { 
          id: 4, 
          imageUrl: 'https://img.freepik.com/premium-photo/chinese-traditional-culture-bagua-diagram_124507-77584.jpg',
          title: '易经智慧解读'
        }
      ]
      this.setData({
        banners: mockBanners
      })
    } catch (error) {
      console.error('获取轮播图失败:', error)
      wx.showToast({
        title: '获取轮播图失败',
        icon: 'none'
      })
    }
  },

  getDailyFortune() {
    try {
      // 生成每日运势数据
      const mockFortune = {
        career: Math.floor(Math.random() * 5) + 1,
        wealth: Math.floor(Math.random() * 5) + 1,
        love: Math.floor(Math.random() * 5) + 1,
        health: Math.floor(Math.random() * 5) + 1
      }
      
      // 根据运势星级生成文字描述
      const average = (mockFortune.career + mockFortune.wealth + mockFortune.love + mockFortune.health) / 4
      let summary = ''
      
      if (average >= 4) {
        summary = '今日运势绝佳，各方面都有不错的表现，是行动的好时机'
      } else if (average >= 3) {
        summary = '今日运势平稳向上，保持积极心态，会有意外收获'
      } else {
        summary = '今日运势较为平淡，建议低调行事，注意休息调养'
      }
      
      mockFortune.summary = summary
      
      this.setData({
        dailyFortune: mockFortune
      })
    } catch (error) {
      console.error('获取运势失败:', error)
      wx.showToast({
        title: '获取运势失败',
        icon: 'none'
      })
    }
  },

  // 功能类型映射
  getFeatureInfo(type) {
    const featureMap = {
      'bazi': { name: '八字排盘', icon: '/assets/icons/home/<USER>', url: '/pages/bazi/bazi' },
      'ziwei': { name: '紫微斗数', icon: '/assets/icons/home/<USER>', url: '/pages/ziwei/ziwei' },
      'wuxing': { name: '五行八卦', icon: '/assets/icons/home/<USER>', url: '/pages/wuxing/wuxing' },
      'yijing': { name: '易经卦象', icon: '/assets/icons/home/<USER>', url: '/pages/yijing/yijing' },
      'fengshui': { name: '风水布局', icon: '/assets/icons/home/<USER>', url: '/pages/fengshui/fengshui' },
      'divination': { name: '事件占卜', icon: '/assets/icons/home/<USER>', url: '/pages/divination/divination' },
      'fortune': { name: '流年运势', icon: '/assets/icons/home/<USER>', url: '/pages/fortune/fortune' }
    }
    return featureMap[type] || null
  },

  // 加载最近使用的功能
  loadRecentUsed() {
    const recentUsed = wx.getStorageSync('recentUsed') || []
    const recentUsedWithInfo = recentUsed.map(item => {
      const featureInfo = this.getFeatureInfo(item.type)
      return {
        ...item,
        ...featureInfo
      }
    }).filter(item => item.name) // 过滤掉无效的类型
    
    this.setData({ recentUsed: recentUsedWithInfo })
  },

  // 记录使用的功能
  recordUsage(type) {
    let recentUsed = wx.getStorageSync('recentUsed') || []
    
    // 移除已存在的相同功能
    recentUsed = recentUsed.filter(item => item.type !== type)
    
    // 添加到开头
    recentUsed.unshift({
      type,
      time: new Date().getTime()
    })
    
    // 只保留最近5个
    if (recentUsed.length > 5) {
      recentUsed = recentUsed.slice(0, 5)
    }
    
    wx.setStorageSync('recentUsed', recentUsed)
    this.loadRecentUsed()
  },

  // 清空最近使用
  clearRecentUsed() {
    wx.showModal({
      title: '提示',
      content: '确定要清空最近使用记录吗？',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('recentUsed')
          this.setData({ recentUsed: [] })
          wx.showToast({
            title: '已清空',
            icon: 'success'
          })
        }
      }
    })
  },

  // 最近使用项目点击
  onRecentItemTap(e) {
    const type = e.currentTarget.dataset.type
    const featureInfo = this.getFeatureInfo(type)
    if (featureInfo) {
      this.navigateTo(featureInfo.url)
    }
  },

  // 获取热门文章
  getHotArticles() {
    try {
      // 模拟热门文章数据
      const mockArticles = [
        {
          id: 1,
          title: '2024年运势分析：紫微斗数看流年变化',
          author: '命理师小李',
          views: 1520,
          cover: '/assets/images/article1.jpg'
        },
        {
          id: 2,
          title: '家居风水布局指南：提升运势的秘诀',
          author: '风水大师',
          views: 890,
          cover: '/assets/images/article2.jpg'
        },
        {
          id: 3,
          title: '八字命理入门：如何看懂自己的命盘',
          author: '易学专家',
          views: 670,
          cover: '/assets/images/article3.jpg'
        }
      ]
      
      this.setData({
        hotArticles: mockArticles
      })
    } catch (error) {
      console.error('获取热门文章失败:', error)
    }
  },

  // 跳转到文章详情
  goToArticle(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/post-detail/post-detail?id=${id}`
    })
  },

  // 跳转到社区
  goToCommunity() {
    wx.switchTab({
      url: '/pages/community/community'
    })
  },

  onBannerTap(e) {
    const { url } = e.currentTarget.dataset;
    this.navigateTo(url);
  },

  checkBirthInfo() {
    const birthInfo = wx.getStorageSync('birthInfo')
    return birthInfo ? true : false
  },

  navigateTo(url) {
    try {
      // 使用统一的导航管理器
      navigationManager.navigateTo(url, {
        success: () => {
          console.log('页面跳转成功:', url)
        },
        fail: (error) => {
          errorHandler.handleNavigationError(error)
        }
      })
    } catch (error) {
      console.error('页面跳转异常:', error)
      errorHandler.handle(error, {
        customMessage: '页面跳转失败，请重试'
      })
    }
  },

  onGridItemTap(e) {
    const url = e.currentTarget.dataset.url
    const type = e.currentTarget.dataset.type
    
    if (type) {
      this.recordUsage(type)
    }
    
    this.navigateTo(url)
  },

  onServiceItemTap(e) {
    const url = e.currentTarget.dataset.url
    const type = e.currentTarget.dataset.type
    
    if (type) {
      this.recordUsage(type)
    }
    
    console.log('服务项点击，准备跳转到:', url)
    this.navigateTo(url)
  },

  onShareAppMessage() {
    return {
      title: '恒琦易道 - 您的命理顾问',
      path: '/pages/index/index',
      imageUrl: '/assets/images/share.jpg'
    }
  },

  bindViewTap() {
    this.navigateTo('../logs/logs')
  },

  onChooseAvatar(e) {
    try {
    const { avatarUrl } = e.detail
    const { nickName } = this.data.userInfo
    this.setData({
      "userInfo.avatarUrl": avatarUrl,
      hasUserInfo: nickName && avatarUrl && avatarUrl !== defaultAvatarUrl,
    })
    } catch (error) {
      console.error('选择头像失败:', error)
    }
  },

  onInputChange(e) {
    const nickName = e.detail.value
    const { avatarUrl } = this.data.userInfo
    this.setData({
      "userInfo.nickName": nickName,
      hasUserInfo: nickName && avatarUrl && avatarUrl !== defaultAvatarUrl,
    })
  },

  getUserProfile(e) {
    // 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认，开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: (res) => {
        console.log(res)
        app.globalData.userInfo = res.userInfo
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        })
      }
    })
  },

  // 检查节气
  checkSolarTerms() {
    const now = new Date()
    const year = now.getFullYear()
    const month = now.getMonth() + 1
    const day = now.getDate()
    
    // 模拟节气数据（实际应该调用云函数）
    const solarTermsData = this.getSolarTermsData(year, month, day)
    
    this.setData({
      solarTerms: solarTermsData
    })
    
    // 如果今天是节气，显示提醒
    if (solarTermsData && solarTermsData.isToday) {
      wx.showToast({
        title: `今天是${solarTermsData.name}`,
        icon: 'none',
        duration: 3000
      })
    }
  },

  // 获取节气数据（简化版本）
  getSolarTermsData(year, month, day) {
    const solarTerms = {
      '2-4': '立春', '2-19': '雨水',
      '3-5': '惊蛰', '3-20': '春分',
      '4-4': '清明', '4-20': '谷雨',
      '5-5': '立夏', '5-21': '小满',
      '6-5': '芒种', '6-21': '夏至',
      '7-7': '小暑', '7-22': '大暑',
      '8-7': '立秋', '8-23': '处暑',
      '9-7': '白露', '9-23': '秋分',
      '10-8': '寒露', '10-23': '霜降',
      '11-7': '立冬', '11-22': '小雪',
      '12-7': '大雪', '12-22': '冬至',
      '1-5': '小寒', '1-20': '大寒'
    }
    
    const key = `${month}-${day}`
    const name = solarTerms[key]
    
    return {
      name: name || '',
      isToday: !!name
    }
  },

  // 检查签到状态
  checkSignIn() {
    const today = new Date().toLocaleDateString()
    const lastSignIn = wx.getStorageSync('lastSignIn')
    
    this.setData({
      signInStatus: today === lastSignIn
    })
  },

  // 签到功能
  async handleSignIn() {
    try {
      const result = await signInManager.signIn();
      if (result.success) {
        this.setData({
          signInStatus: true
        });
        
        // 构建奖励提示信息
        let message = `签到成功！获得${result.pointsEarned}积分\n`;
        if (result.rewards && result.rewards.length > 1) {
          message += '\n获得奖励：\n';
          result.rewards.forEach(reward => {
            message += `${reward.description}：+${reward.points}积分\n`;
          });
        }
        message += `\n连续签到：${result.consecutiveDays}天`;
        message += `\n本月签到：${result.monthlyDays}天`;
        
        wx.showModal({
          title: '签到成功',
          content: message,
          showCancel: false,
          confirmText: '我知道了',
          success: () => {
            // 更新页面数据
            this.checkSignIn();
          }
        });
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('签到失败:', error);
      wx.showToast({
        title: '签到失败，请重试',
        icon: 'none'
      });
    }
  },

  // 轮播图点击事件
  onSwiperTap(e) {
    const { url } = e.currentTarget.dataset
    this.navigateTo(url)
  },

  // 导航项点击事件
  onNavTap(e) {
    const { url } = e.currentTarget.dataset
    this.navigateTo(url)
  },

  // 分类项目点击
  onCategoryTap(e) {
    const url = e.currentTarget.dataset.url
    this.navigateTo(url)
  },
})
